import { icon } from '../constants/icon';
import { useEffect } from 'react';
import { Pressable, StyleSheet } from 'react-native';
import Animated, { interpolate, useAnimatedStyle, useSharedValue, withSpring } from 'react-native-reanimated';

const TabBarButton = ({onPress, onLongPress, isFocused, routeName, color, label} :{
    onPress: Function; 
    onLongPress: Function; 
    isFocused: boolean; 
    routeName: string; 
    color: string;
    label: string

}) => {
    const scale = useSharedValue(0);

    useEffect(() => {
        scale.value = withSpring(
            typeof isFocused === 'boolean' ? (isFocused ? 1 : 0) : isFocused, {
            duration: 350
        });
    }, [scale, isFocused]);

    const animatedTextStyle = useAnimatedStyle(() => {
        const opacity = interpolate(scale.value, [0, 1], [1, 0]);
        return {
            opacity
        }
    });

    // Special styling for create button
    const isCreateButton = routeName === 'create';
    const iconSize = isCreateButton ? 32 : 24; // Larger size for create button

    const animatedIconStyle = useAnimatedStyle(() => {
        const scaleValue = interpolate(scale.value, [0, 1], [1, 1.1]);
        // For create button, keep it centered (no top offset when focused)
        // For other buttons, move down when focused to make room for disappearing text
        const top = interpolate(scale.value, [0, 1], [0, isCreateButton ? 0 : 9]);

        return {
            transform: [{scale: scaleValue}],
            top: top
        }
    });


    return (
        <Pressable
            onPress={onPress as (event: import('react-native').GestureResponderEvent) => void}
            onLongPress={onLongPress as (event: import('react-native').GestureResponderEvent) => void}
            style={styles.tabbaritem}
        >
            <Animated.View style={animatedIconStyle}>
            {icon[routeName as keyof typeof icon]?.({
                color: isFocused ? "white" : "black",
                size: iconSize
            })}
            </Animated.View>

            {!isCreateButton && (
                <Animated.Text style={[animatedTextStyle, { color: isFocused ? "green" : "black", fontSize: 12}, animatedTextStyle ]}>{label}</Animated.Text>
            )}
        </Pressable>
    )
}

export default TabBarButton;

const styles = StyleSheet.create({
    tabbaritem: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        gap: 2
    }
})