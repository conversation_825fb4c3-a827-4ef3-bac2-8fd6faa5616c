// File: app/(tabs)/create.tsx
import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  StatusBar,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import RNPickerSelect from 'react-native-picker-select'; // For Academic Level
import ThemeSelectItem, { Theme } from '../../components/ThemeSelectItem'; // Adjust path

// --- Constants & Dummy Data ---
const APP_PRIMARY_COLOR = '#5A67D8';
const APP_BACKGROUND_COLOR = '#000000'
const INPUT_BACKGROUND_COLOR = '#1C1C1E';
const TEXT_COLOR_PRIMARY = '#FFFFFF';
const TEXT_COLOR_SECONDARY = '#A0A0A0';
const TEXT_COLOR_PLACEHOLDER = '#6E6E72';

const ACADEMIC_LEVELS = [
  { label: 'High School', value: 'high_school' },
  { label: 'Undergraduate', value: 'undergraduate' },
  { label: 'Graduate', value: 'graduate' },
  { label: 'Professional', value: 'professional' },
  { label: 'Any', value: 'any' },
];

const AVAILABLE_THEMES: Theme[] = [
  { id: 'matrix', name: 'Matrix', iconName: 'code-slash-outline' },
  { id: 'solar_punk', name: 'Solar Punk', iconName: 'leaf-outline' },
  { id: 'interstellar', name: 'Interstellar', iconName: 'planet-outline' },
  { id: 'pop_pastel', name: 'Pop Pastel', iconName: 'color-palette-outline' },
  { id: 'cyberpunk', name: 'Cyberpunk', iconName: 'hardware-chip-outline' },
  // Add more themes
];

export default function CreateScreen() {
  const router = useRouter();

  // --- State Management  ---
  const [description, setDescription] = useState('');
  const [academicLevel, setAcademicLevel] = useState<string | null>(null);
  const [selectedTheme, setSelectedTheme] = useState<Theme | null>(null); // Single theme selection for simplicity first

  // --- Event Handlers ---
  const handleClose = () => {
    // Could prompt user if they have unsaved changes
    router.back(); // Or navigate to Explore if coming from a different context
  };

  // Generate Button Logic (Initial)
  const handleGenerate = () => {
    if (!description.trim()) {
      Alert.alert('Missing Description', 'Please describe your simulation.');
      return;
    }
    if (!academicLevel) {
      Alert.alert('Missing Level', 'Please select an academic level.');
      return;
    }
    if (!selectedTheme) {
      Alert.alert('Missing Theme', 'Please select a theme.');
      return;
    }

    const collectedData = {
      description,
      academicLevel,
      theme: selectedTheme.id, // Store theme ID or name
      // Add title if you have a title input field
    };
    console.log('Collected Simulation Data:', collectedData);
    Alert.alert('Data Collected', 'Check the console for the simulation data. Backend integration is next!');
    // TODO: Call backend service to create the simulation
    // e.g., createSimulationInFirestore(collectedData)
    // Then navigate or show success message
  };

  const handleThemeSelect = useCallback((theme: Theme) => {
    setSelectedTheme(prevTheme => (prevTheme?.id === theme.id ? null : theme)); // Toggle selection
  }, []);

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar barStyle="light-content" backgroundColor={APP_BACKGROUND_COLOR} />
      {/* Task 4.1.1: Header Configuration */}
      <Stack.Screen
        options={{
          headerShown: true,
          title: '', // No title text needed next to X
          headerStyle: { backgroundColor: APP_BACKGROUND_COLOR },
          headerShadowVisible: false, // No shadow under header
            
          // headerRight: () => ( // Optional: if you want a save draft button or similar
          //   <TouchableOpacity onPress={() => console.log("Save Draft")} style={styles.headerButton}>
          //     <Ionicons name="save-outline" size={24} color={TEXT_COLOR_PRIMARY} />
          //   </TouchableOpacity>
          // ),
        }}
      />

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.container}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 60 : 0} // Adjust as needed
      >
        <ScrollView contentContainerStyle={styles.scrollContentContainer} keyboardShouldPersistTaps="handled">
          {/*  Description Input */}
          <View style={styles.inputContainer}>
            <TextInput
              style={styles.descriptionInput}
              placeholder="Describe your simulation..."
              placeholderTextColor={TEXT_COLOR_PLACEHOLDER}
              multiline={true}
              value={description}
              onChangeText={setDescription}
              textAlignVertical="top" // For Android multiline
            />
            {/*  Generate Button (Up Arrow Icon) */}
            <TouchableOpacity style={styles.generateButton} onPress={handleGenerate}>
              <MaterialIcons name="arrow-upward" size={28} color={TEXT_COLOR_PRIMARY} />
            </TouchableOpacity>
          </View>

          {/*  Academic Level Section */}
          <Text style={styles.sectionTitle}>Academic Level</Text>
          <View style={styles.pickerContainer}>
            <RNPickerSelect
              placeholder={{ label: 'Select Level', value: null, color: TEXT_COLOR_PLACEHOLDER }}
              items={ACADEMIC_LEVELS}
              onValueChange={(value) => setAcademicLevel(value)}
              value={academicLevel}
              style={pickerSelectStyles}
              useNativeAndroidPickerStyle={false} // To apply custom styles on Android
              Icon={() => <Ionicons name="chevron-down" size={20} color={TEXT_COLOR_SECONDARY} style={styles.pickerIcon} />}
            />
          </View>

          {/*  Themes Section */}
          <Text style={styles.sectionTitle}>Themes</Text>
          <ScrollView
            horizontal={true}
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.themesScrollContainer}
          >
            {AVAILABLE_THEMES.map(theme => (
              <ThemeSelectItem
                key={theme.id}
                theme={theme}
                isSelected={selectedTheme?.id === theme.id}
                onPress={() => handleThemeSelect(theme)}
              />
            ))}
          </ScrollView>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

// --- Styles ---
const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: APP_BACKGROUND_COLOR,
  },
  container: {
    flex: 1,
  },
  scrollContentContainer: {
    padding: 20,
  },
  headerButton: {
    paddingHorizontal: 10, // For easier touch
  },
  inputContainer: {
    backgroundColor: INPUT_BACKGROUND_COLOR,
    borderRadius: 12,
    padding: 15,
    minHeight: 200, // As per screenshot
    justifyContent: 'space-between', // To push button to bottom right
    marginBottom: 30,
  },
  descriptionInput: {
    flex: 1, // Take available space before button
    color: TEXT_COLOR_PRIMARY,
    fontSize: 16,
    lineHeight: 22,
  },
  generateButton: {
    position: 'absolute', // Position relative to inputContainer
    bottom: 15,
    right: 15,
    backgroundColor: APP_PRIMARY_COLOR,
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sectionTitle: {
    color: TEXT_COLOR_PRIMARY,
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  pickerContainer: {
    backgroundColor: INPUT_BACKGROUND_COLOR,
    borderRadius: 12,
    marginBottom: 30,
    // paddingHorizontal: 10, // RNPickerSelect handles some padding
  },
  pickerIcon: {
    marginRight: 15, // Position icon inside the picker visually
    marginTop: Platform.OS === 'ios' ? 0 : 12, // Adjust vertical alignment
  },
  themesScrollContainer: {
    paddingVertical: 10,
  },
});

const pickerSelectStyles = StyleSheet.create({
  inputIOS: {
    fontSize: 16,
    paddingVertical: 15,
    paddingHorizontal: 15,
    color: TEXT_COLOR_PRIMARY,
    backgroundColor: INPUT_BACKGROUND_COLOR, // Ensure consistent background
    borderRadius: 12, // Match container
  },
  inputAndroid: {
    fontSize: 16,
    paddingVertical: 15,
    paddingHorizontal: 15,
    color: TEXT_COLOR_PRIMARY,
    backgroundColor: INPUT_BACKGROUND_COLOR, // Ensure consistent background
    borderRadius: 12, // Match container
  },
  placeholder: {
    color: TEXT_COLOR_PLACEHOLDER,
  },
  iconContainer: { // Style for the view holding the icon
    top: Platform.OS === 'ios' ? 0 : 12, // Center icon vertically
    right: 15,
  },
});
